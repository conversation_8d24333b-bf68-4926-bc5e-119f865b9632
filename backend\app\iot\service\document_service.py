#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文档管理服务层

基于RAGFlow API实现文档管理的纯代理服务
严格遵循指南中的API优先策略和参数预处理策略
"""
import os
import mimetypes
import io
from typing import List, Optional, BinaryIO
import httpx
from fastapi import HTTPException, UploadFile
from loguru import logger

from backend.app.iot.schema.document import (
    DocumentUpload,
    DocumentUpdate,
    DocumentQuery,
    DocumentDelete,
    DocumentParseControl,
    RAGFlowDocumentUpload,
    RAGFlowDocumentUpdate,
    RAGFlowDocumentQuery,
    RAGFlowDocumentDelete,
    FileValidation
)
from backend.app.iot.utils.file_upload import file_upload_handler

from backend.core.conf import settings


class DocumentService:
    """文档管理服务类 - 纯代理模式，直接调用RAGFlow API"""

    def __init__(self):
        # RAGFlow服务配置
        self.ragflow_base_url = getattr(settings, 'KNOWLEDGE_BASE_URL', 'http://192.168.66.13:9222')
        self.ragflow_api_key = getattr(settings, 'KNOWLEDGE_BASE_API_KEY', 'ragflow-c2ZmY3MzVhMDkwYzExZjA5MzIwZjZjZW')
        self.timeout = getattr(settings, 'KNOWLEDGE_BASE_TIMEOUT', 30.0)

        # 文件验证配置
        self.file_validation = FileValidation()

    async def _make_doc_request(self, method: str, endpoint: str, **kwargs) -> dict:
        """
        统一的RAGFlow文档请求处理 - 保持原始错误信息

        :param method: HTTP 方法
        :param endpoint: API 端点
        :param kwargs: 请求参数 (data, params, json, files等)
        :return: 响应数据
        """
        url = f"{self.ragflow_base_url}{endpoint}"
        headers = {
            "Authorization": f"Bearer {self.ragflow_api_key}"
        }

        # 如果不是文件上传，设置Content-Type
        if 'files' not in kwargs:
            headers["Content-Type"] = "application/json"

        # 针对文件上传操作使用更长的超时时间
        upload_timeout = self.timeout * 3 if 'files' in kwargs else self.timeout

        async with httpx.AsyncClient(
            timeout=httpx.Timeout(upload_timeout, connect=10.0),
            verify=False,
            follow_redirects=True
        ) as client:
            try:
                response = await client.request(
                    method=method.upper(),
                    url=url,
                    headers=headers,
                    **kwargs
                )

                # 检查响应状态码
                if response.status_code == 404:
                    raise HTTPException(status_code=404, detail="文档未找到")
                elif response.status_code != 200:
                    raise HTTPException(status_code=response.status_code, detail=f"RAGFlow服务错误: {response.status_code}")

                # 尝试解析JSON响应
                try:
                    result = response.json()

                    # 记录RAGFlow响应用于调试
                    logger.debug(f"RAGFlow响应: {result}")

                    # 检查RAGFlow API的业务错误码
                    # RAGFlow成功响应的code是0，失败时是非0值
                    code = result.get("code")
                    if code is not None and code != 0:
                        error_message = result.get("message") or result.get("msg", "RAGFlow文档服务业务错误")
                        logger.error(f"RAGFlow业务错误: code={code}, message={error_message}")
                        raise HTTPException(
                            status_code=400,
                            detail=error_message
                        )

                    return result

                except ValueError as json_error:
                    # JSON解析失败，可能是文件流或其他格式
                    logger.warning(f"RAGFlow响应不是JSON格式: {str(json_error)}")
                    return {
                        "code": 0,
                        "data": {
                            "content": response.content,
                            "content_type": response.headers.get("content-type", "application/octet-stream"),
                            "status_code": response.status_code
                        }
                    }

            except httpx.TimeoutException:
                # 针对文件上传超时，提供更详细的错误信息
                if 'files' in kwargs:
                    logger.error(f"RAGFlow文档服务文件上传超时: {url}, 超时时间: {upload_timeout}秒")
                    raise HTTPException(
                        status_code=504,
                        detail=f"文档上传超时，请检查文件大小或网络连接。如果文件较大，请稍后查看文档列表确认上传状态。"
                    )
                else:
                    logger.error(f"RAGFlow文档服务请求超时: {url}")
                    raise HTTPException(status_code=504, detail="RAGFlow文档服务请求超时")
            except httpx.RequestError as e:
                logger.error(f"RAGFlow文档服务请求失败: {str(e)}")
                raise HTTPException(status_code=503, detail=f"RAGFlow文档服务不可用: {str(e)}")

    # 新增：符合RAGFlow API规范的方法

    async def upload_documents(self, dataset_id: str, files: List[UploadFile]) -> dict:
        """
        上传文档到数据集 - 符合RAGFlow API规范

        :param dataset_id: 数据集ID
        :param files: 上传的文件列表
        :return: RAGFlow响应数据
        """
        # 准备文件数据
        files_data = []
        for file in files:
            # 验证文件
            if not self.file_validation.validate_file(file):
                raise HTTPException(status_code=400, detail=f"不支持的文件类型: {file.filename}")

            # 读取文件内容
            content = await file.read()
            files_data.append(("file", (file.filename, content, file.content_type)))

        return await self._make_doc_request(
            "POST",
            f"/api/v1/datasets/{dataset_id}/documents",
            files=files_data
        )

    async def list_documents_ragflow(self, query_params: dict) -> dict:
        """
        列出数据集中的文档 - 符合RAGFlow API规范

        :param query_params: 查询参数
        :return: RAGFlow响应数据
        """
        dataset_id = query_params.pop("dataset_id")

        # 过滤None值
        params = {k: v for k, v in query_params.items() if v is not None}

        return await self._make_doc_request(
            "GET",
            f"/api/v1/datasets/{dataset_id}/documents",
            params=params
        )

    async def delete_documents_ragflow(self, dataset_id: str, doc_ids: List[str]) -> dict:
        """
        删除数据集中的文档 - 符合RAGFlow API规范

        :param dataset_id: 数据集ID
        :param doc_ids: 文档ID列表
        :return: RAGFlow响应数据
        """
        # 安全检查：必须指定要删除的文档ID，防止误删所有文档
        if not doc_ids:
            raise HTTPException(
                status_code=400,
                detail="必须指定要删除的文档ID列表，不能为空"
            )

        data = {"ids": doc_ids}

        return await self._make_doc_request(
            "DELETE",
            f"/api/v1/datasets/{dataset_id}/documents",
            json=data
        )

    async def update_document_ragflow(self, dataset_id: str, document_id: str, update_data: dict) -> dict:
        """
        更新文档信息 - 符合RAGFlow API规范

        :param dataset_id: 数据集ID
        :param document_id: 文档ID
        :param update_data: 更新数据
        :return: RAGFlow响应数据
        """
        return await self._make_doc_request(
            "PUT",
            f"/api/v1/datasets/{dataset_id}/documents/{document_id}",
            json=update_data
        )

    async def download_document_ragflow(self, dataset_id: str, document_id: str) -> dict:
        """
        下载文档 - 符合RAGFlow API规范

        :param dataset_id: 数据集ID
        :param document_id: 文档ID
        :return: 文件流数据
        """
        result = await self._make_doc_request(
            "GET",
            f"/api/v1/datasets/{dataset_id}/documents/{document_id}"
        )

        # 处理文件流响应
        if isinstance(result.get("data"), bytes):
            return {
                "content": io.BytesIO(result["data"]),
                "content_type": result.get("content_type", "application/octet-stream"),
                "filename": f"document_{document_id}"
            }

        return result

    async def parse_documents_ragflow(self, dataset_id: str, document_ids: List[str]) -> dict:
        """
        解析文档 - 符合RAGFlow API规范

        :param dataset_id: 数据集ID
        :param document_ids: 文档ID列表
        :return: RAGFlow响应数据
        """
        data = {"document_ids": document_ids}

        return await self._make_doc_request(
            "POST",
            f"/api/v1/datasets/{dataset_id}/chunks",
            json=data
        )

    async def stop_parsing_documents_ragflow(self, dataset_id: str, document_ids: List[str]) -> dict:
        """
        停止解析文档 - 符合RAGFlow API规范

        :param dataset_id: 数据集ID
        :param document_ids: 文档ID列表
        :return: RAGFlow响应数据
        """
        data = {"document_ids": document_ids}

        return await self._make_doc_request(
            "DELETE",
            f"/api/v1/datasets/{dataset_id}/chunks",
            json=data
        )

    # 保留原有方法以保持向后兼容性

    def _validate_file(self, file: UploadFile) -> None:
        """
        验证上传文件

        :param file: 上传的文件
        :raises HTTPException: 文件验证失败
        """
        # 检查文件大小
        if file.size and file.size > self.file_validation.max_size:
            raise HTTPException(
                status_code=413,
                detail=f"文件大小超过限制 {self.file_validation.max_size / 1024 / 1024:.1f}MB"
            )

        # 检查文件类型
        file_ext = os.path.splitext(file.filename or "")[1].lower().lstrip('.')
        if file_ext not in self.file_validation.allowed_types:
            raise HTTPException(
                status_code=415,
                detail=f"不支持的文件类型: {file_ext}，支持的类型: {', '.join(self.file_validation.allowed_types)}"
            )

        # 检查MIME类型
        if file.content_type and file.content_type not in self.file_validation.allowed_mime_types:
            # 尝试根据文件扩展名推断MIME类型
            guessed_type, _ = mimetypes.guess_type(file.filename or "")
            if guessed_type not in self.file_validation.allowed_mime_types:
                raise HTTPException(
                    status_code=415,
                    detail=f"不支持的MIME类型: {file.content_type}"
                )

    def _preprocess_upload_params(self, upload_data: DocumentUpload) -> RAGFlowDocumentUpload:
        """
        预处理上传参数 - 遵循指南中的参数预处理策略

        :param upload_data: 前端上传请求
        :return: RAGFlow API格式的参数
        """
        # 设置默认解析器配置
        default_parser_config = {
            "chunk_token_count": 128,
            "layout_recognize": True,
            "html4excel": False,
            "delimiter": "\\n!?。；！？",
            "task_page_size": 12
        }

        # 合并用户配置
        parser_config = default_parser_config.copy()
        if upload_data.parser_config:
            parser_config.update(upload_data.parser_config)

        return RAGFlowDocumentUpload(
            parser_id=upload_data.parser_id or "naive",
            parser_config=parser_config,
            run=upload_data.run_after_upload
        )

    def _preprocess_query_params(self, query_data: DocumentQuery) -> RAGFlowDocumentQuery:
        """
        预处理查询参数

        :param query_data: 前端查询请求
        :return: RAGFlow API格式的参数
        """
        return RAGFlowDocumentQuery(
            page=query_data.page,
            page_size=query_data.page_size,
            orderby=query_data.orderby,
            desc=query_data.desc,
            keywords=query_data.keywords
        )

    async def upload_document(self, kb_id: str, file: UploadFile, upload_data: DocumentUpload, upload_id: Optional[str] = None) -> dict:
        """
        上传文档到指定知识库

        :param kb_id: 知识库ID
        :param file: 上传的文件
        :param upload_data: 上传参数
        :param upload_id: 上传ID，用于进度跟踪
        :return: RAGFlow响应数据
        """
        # 使用文件上传处理器进行高级处理
        file_content, file_info = await file_upload_handler.upload_file_with_progress(file, upload_id)

        # 预处理参数
        ragflow_params = self._preprocess_upload_params(upload_data)

        # 准备文件数据
        files = {
            "file": (file.filename, file_content, file.content_type)
        }

        # 准备表单数据
        import json
        data = {
            "parser_id": ragflow_params.parser_id,
            "parser_config": json.dumps(ragflow_params.parser_config),  # 转换为JSON字符串
            "run": ragflow_params.run
        }

        try:
            result = await self._make_doc_request(
                "POST",
                f"/api/v1/datasets/{kb_id}/documents",
                files=files,
                data=data
            )

            # 调试：打印RAGFlow API响应结构
            logger.info(f"RAGFlow上传API响应: {result}")
            logger.info(f"响应data类型: {type(result.get('data'))}")
            logger.info(f"响应data内容: {result.get('data')}")

            # 添加文件信息到响应
            if "data" in result:
                # RAGFlow API返回的data可能是列表或字典，需要适配
                if isinstance(result["data"], list):
                    # 如果是列表，为每个文档添加文件信息
                    for doc in result["data"]:
                        if isinstance(doc, dict):
                            doc["file_info"] = file_info
                elif isinstance(result["data"], dict):
                    # 如果是字典，直接添加文件信息
                    result["data"]["file_info"] = file_info
                else:
                    # 如果都不是，创建新的响应结构
                    result["data"] = {
                        "upload_result": result["data"],
                        "file_info": file_info
                    }

            return result

        finally:
            # 清理上传进度回调
            if upload_id:
                file_upload_handler.unregister_progress_callback(upload_id)

    async def list_documents(self, query_data: DocumentQuery) -> dict:
        """
        获取文档列表

        :param query_data: 查询参数
        :return: RAGFlow响应数据
        """
        ragflow_params = self._preprocess_query_params(query_data)
        params = ragflow_params.model_dump(exclude_none=True)

        return await self._make_doc_request(
            "GET",
            f"/api/v1/datasets/{query_data.kb_id}/documents",
            params=params
        )

    async def get_document(self, kb_id: str, doc_id: str) -> dict:
        """
        获取文档详情

        :param kb_id: 知识库ID
        :param doc_id: 文档ID
        :return: RAGFlow响应数据
        """
        return await self._make_doc_request(
            "GET",
            f"/api/v1/datasets/{kb_id}/documents/{doc_id}"
        )

    async def download_document(self, kb_id: str, doc_id: str) -> dict:
        """
        下载文档

        :param kb_id: 知识库ID
        :param doc_id: 文档ID
        :return: 文档内容和元数据
        """
        try:
            # 首先获取文档信息
            doc_info = await self.get_document(kb_id, doc_id)
            doc_data = doc_info.get("data", {})
            doc_name = doc_data.get("name", "document")

            # 调用RAGFlow下载API - 使用正确的下载端点
            result = await self._make_doc_request(
                "GET",
                f"/api/v1/datasets/{kb_id}/documents/{doc_id}"
            )

            # 从响应中提取文件内容
            data = result.get("data", {})
            content = data.get("content")
            content_type = data.get("content_type", "application/octet-stream")

            if content is None:
                raise HTTPException(status_code=404, detail="文档内容未找到")

            # 如果content是bytes，创建BytesIO流
            if isinstance(content, bytes):
                content_stream = io.BytesIO(content)
            elif isinstance(content, str):
                # 如果是字符串，编码为bytes后创建流
                content_stream = io.BytesIO(content.encode('utf-8'))
                if not content_type or content_type == "application/octet-stream":
                    content_type = "text/plain; charset=utf-8"
            else:
                raise HTTPException(status_code=500, detail="不支持的文档内容格式")

            return {
                "content": content_stream,
                "content_type": content_type,
                "filename": doc_name
            }

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"下载文档失败: {str(e)}")
            raise HTTPException(status_code=500, detail=f"下载文档失败: {str(e)}")



    async def update_document(self, kb_id: str, doc_id: str, update_data: DocumentUpdate) -> dict:
        """
        更新文档信息

        :param kb_id: 知识库ID
        :param doc_id: 文档ID
        :param update_data: 更新数据
        :return: RAGFlow响应数据
        """
        # 预处理参数
        ragflow_params = RAGFlowDocumentUpdate(
            name=update_data.name,
            parser_id=update_data.parser_id,
            parser_config=update_data.parser_config
        )

        data = ragflow_params.model_dump(exclude_none=True)

        return await self._make_doc_request(
            "PUT",
            f"/api/v1/datasets/{kb_id}/documents/{doc_id}",
            json=data
        )

    async def delete_documents(self, delete_data: DocumentDelete) -> dict:
        """
        删除文档

        :param delete_data: 删除参数
        :return: RAGFlow响应数据
        """
        # 安全检查：必须指定要删除的文档ID，防止误删所有文档
        if not delete_data.doc_ids:
            raise HTTPException(
                status_code=400,
                detail="必须指定要删除的文档ID列表，不能为空"
            )

        # 使用正确的RAGFlow API参数格式
        data = {"ids": delete_data.doc_ids}

        return await self._make_doc_request(
            "DELETE",
            f"/api/v1/datasets/{delete_data.kb_id}/documents",
            json=data
        )

    async def start_document_parsing(self, parse_control: DocumentParseControl) -> dict:
        """
        开始文档解析 - 使用RAGFlow的document/run API进行重新解析

        :param parse_control: 解析控制参数
        :return: RAGFlow响应数据
        """
        # 使用RAGFlow的document/run API，这个API专门用于重新解析文档
        # 它会自动重置文档状态并开始解析，无需手动检查进度
        data = {
            "doc_ids": [parse_control.doc_id],
            "run": 1  # 1表示开始解析
        }

        logger.info(f"使用document/run API重新解析文档: {parse_control.doc_id}")

        return await self._make_doc_request(
            "POST",
            "/v1/document/run",
            json=data
        )

    async def stop_document_parsing(self, kb_id: str, doc_id: str) -> dict:
        """
        停止文档解析

        :param kb_id: 知识库ID
        :param doc_id: 文档ID
        :return: RAGFlow响应数据
        """
        # RAGFlow停止解析API使用 /api/v1/datasets/{dataset_id}/chunks 端点
        # 参数格式: {"document_ids": ["doc_id1", "doc_id2"]}
        data = {
            "document_ids": [doc_id]
        }

        return await self._make_doc_request(
            "DELETE",
            f"/api/v1/datasets/{kb_id}/chunks",
            json=data
        )

    # ==================== 文档解析结果查看相关方法 ====================

    async def get_document_chunks(self, dataset_id: str, document_id: str, query_params: dict) -> dict:
        """
        获取文档分块列表 - 符合RAGFlow API规范

        :param dataset_id: 数据集ID
        :param document_id: 文档ID
        :param query_params: 查询参数
        :return: RAGFlow响应数据
        """
        # 过滤None值参数
        filtered_params = {k: v for k, v in query_params.items() if v is not None}

        return await self._make_doc_request(
            "GET",
            f"/api/v1/datasets/{dataset_id}/documents/{document_id}/chunks",
            params=filtered_params
        )

    async def retrieve_document_chunks(self, retrieval_data: dict) -> dict:
        """
        检索文档分块（带搜索功能） - 符合RAGFlow API规范

        :param retrieval_data: 检索参数
        :return: RAGFlow响应数据
        """
        return await self._make_doc_request(
            "POST",
            "/api/v1/retrieval",
            json=retrieval_data
        )

    async def get_document_parse_result(self, dataset_id: str, document_id: str) -> dict:
        """
        获取文档解析结果统计信息

        :param dataset_id: 数据集ID
        :param document_id: 文档ID
        :return: RAGFlow响应数据
        """
        try:
            # 首先获取文档详情
            doc_result = await self.get_document(dataset_id, document_id)

            # 然后获取分块统计信息
            chunks_result = await self.get_document_chunks(dataset_id, document_id, {"page": 1, "page_size": 1})

            # 合并统计信息
            doc_data = doc_result.get("data", {})
            chunks_data = chunks_result.get("data", {})

            parse_result = {
                "document": doc_data,
                "chunk_count": chunks_data.get("total", 0),
                "chunks_preview": chunks_data.get("chunks", [])[:5]  # 只返回前5个分块作为预览
            }

            return {"code": 0, "data": parse_result}

        except Exception as e:
            logger.error(f"获取文档解析结果失败: {str(e)}")
            raise HTTPException(status_code=500, detail=f"获取文档解析结果失败: {str(e)}")

    # ==================== 文档分块CRUD操作方法 ====================

    async def create_document_chunk(self, dataset_id: str, document_id: str, chunk_data: dict) -> dict:
        """
        创建新的文档分块 - 符合RAGFlow API规范

        :param dataset_id: 数据集ID
        :param document_id: 文档ID
        :param chunk_data: 分块数据
        :return: RAGFlow响应数据
        """
        try:
            # 构建符合RAGFlow API规范的请求数据
            request_data = {
                "content": chunk_data.get("content", ""),
                "important_keywords": chunk_data.get("important_keywords", []),
                "questions": chunk_data.get("questions", [])
            }

            logger.info(f"创建文档分块: dataset_id={dataset_id}, document_id={document_id}")
            logger.debug(f"分块数据: {request_data}")

            result = await self._make_doc_request(
                "POST",
                f"/api/v1/datasets/{dataset_id}/documents/{document_id}/chunks",
                json=request_data
            )

            logger.info(f"创建文档分块成功: {result}")
            return result

        except Exception as e:
            logger.error(f"创建文档分块失败: {str(e)}")
            raise HTTPException(status_code=500, detail=f"创建文档分块失败: {str(e)}")

    async def update_document_chunk(self, dataset_id: str, document_id: str, chunk_id: str, chunk_data: dict) -> dict:
        """
        更新指定文档分块 - 符合RAGFlow API规范
        基于test_chunk_update.py的成功实现进行优化

        :param dataset_id: 数据集ID
        :param document_id: 文档ID
        :param chunk_id: 分块ID
        :param chunk_data: 分块数据
        :return: RAGFlow响应数据
        """
        try:
            # 1. 数据验证 - 确保必要字段存在
            if not chunk_data.get("content"):
                raise HTTPException(status_code=400, detail="分块内容不能为空")

            # 2. 构建符合RAGFlow API规范的请求数据
            # 基于测试脚本的成功格式：{"content": "...", "important_keywords": [...]}
            request_data = {
                "content": chunk_data["content"],
                "important_keywords": chunk_data.get("important_keywords", [])
            }

            # 可选字段
            if "available" in chunk_data:
                request_data["available"] = chunk_data["available"]

            logger.info(f"🔄 开始更新文档分块: dataset_id={dataset_id}, document_id={document_id}, chunk_id={chunk_id}")
            logger.debug(f"📝 更新数据: {request_data}")

            # 3. 构建完整的RAGFlow API URL用于日志
            ragflow_url = f"{self.ragflow_base_url}/api/v1/datasets/{dataset_id}/documents/{document_id}/chunks/{chunk_id}"
            logger.info(f"📡 调用RAGFlow API: PUT {ragflow_url}")

            # 4. 执行更新请求
            result = await self._make_doc_request(
                "PUT",
                f"/api/v1/datasets/{dataset_id}/documents/{document_id}/chunks/{chunk_id}",
                json=request_data
            )

            logger.info(f"📊 RAGFlow API响应: {result}")

            # 5. 验证更新结果
            if result.get("code") == 0:
                logger.info(f"✅ 分块更新成功确认: chunk_id={chunk_id}")

                # 6. 添加数据同步验证机制（基于测试脚本的验证逻辑）
                # 注意：由于ES的refresh机制，更新可能需要一些时间才能在搜索中可见
                # 这里记录成功状态，前端可以通过延迟刷新来获取最新数据
                logger.info(f"💾 分块内容已更新，建议前端延迟2-3秒后刷新以获取最新数据")

            else:
                error_msg = result.get("message") or result.get("msg", "未知错误")
                logger.warning(f"⚠️ 分块更新响应异常: code={result.get('code')}, message={error_msg}")
                # 不抛出异常，让调用方处理业务错误码

            return result

        except HTTPException:
            # 重新抛出HTTP异常
            raise
        except Exception as e:
            logger.error(f"❌ 更新文档分块失败: {str(e)}")
            raise HTTPException(status_code=500, detail=f"更新文档分块失败: {str(e)}")

    async def delete_document_chunks(self, dataset_id: str, document_id: str, delete_data: dict) -> dict:
        """
        删除指定文档分块 - 符合RAGFlow API规范

        :param dataset_id: 数据集ID
        :param document_id: 文档ID
        :param delete_data: 删除数据（包含chunk_ids列表）
        :return: RAGFlow响应数据
        """
        try:
            # 构建符合RAGFlow API规范的请求数据
            request_data = {
                "chunk_ids": delete_data.get("chunk_ids", [])
            }

            logger.info(f"删除文档分块: dataset_id={dataset_id}, document_id={document_id}")
            logger.debug(f"删除分块IDs: {request_data['chunk_ids']}")

            result = await self._make_doc_request(
                "DELETE",
                f"/api/v1/datasets/{dataset_id}/documents/{document_id}/chunks",
                json=request_data
            )

            logger.info(f"删除文档分块成功: {result}")
            return result

        except Exception as e:
            logger.error(f"删除文档分块失败: {str(e)}")
            raise HTTPException(status_code=500, detail=f"删除文档分块失败: {str(e)}")

    async def get_document_preview(self, kb_id: str, doc_id: str, doc_name: str = None) -> dict:
        """
        获取文档预览内容 - 简化版本，直接使用RAGFlow API

        :param kb_id: 知识库ID
        :param doc_id: 文档ID
        :return: 预览内容
        """
        try:
            # 获取文档信息
            doc_info = await self.get_document(kb_id, doc_id)
            doc_data = doc_info.get("data", {})

            # 优先使用前端传递的文档名称
            if not doc_name:
                doc_name = doc_data.get("name", "")

            # 调试信息 - 只记录关键字段，避免打印二进制数据
            logger.info(f"文档预览调试 - doc_id: {doc_id}")
            logger.info(f"doc_name: '{doc_name}', doc_data keys: {list(doc_data.keys())}")

            # 记录文档基本信息（排除可能的二进制内容）
            safe_fields = {k: v for k, v in doc_data.items()
                          if k not in ['content', 'binary', 'blob', 'data', 'file_content']
                          and not isinstance(v, bytes)}
            logger.info(f"RAGFlow返回的文档信息: {safe_fields}")

            # 如果文档名为空，尝试从其他字段获取
            if not doc_name:
                # 尝试多个可能的字段名
                possible_names = [
                    doc_data.get("filename"),
                    doc_data.get("title"),
                    doc_data.get("file_name"),
                    doc_data.get("document_name"),
                    doc_data.get("original_name")
                ]

                for name in possible_names:
                    if name and name.strip():
                        doc_name = name.strip()
                        logger.info(f"找到文档名: '{doc_name}'")
                        break



                # 如果还是没有找到，使用默认名称（不添加扩展名，让后续的文件头检测决定类型）
                if not doc_name:
                    doc_name = f"document_{doc_id}"
                    logger.warning(f"无法获取文档名，使用默认名称: '{doc_name}'")

            # 检查RAGFlow返回的实际内容来确定类型
            content = doc_data.get("content")
            doc_type = "unknown"  # 默认类型

            if content and isinstance(content, bytes):
                # 优先使用文件头来确定实际类型
                if content.startswith(b'PK\x03\x04'):
                    # ZIP格式，通常是Office文档 (docx, xlsx, pptx)
                    doc_type = "office"
                    logger.info("根据文件头检测到Office文档格式(ZIP)，修正类型为office")
                elif content.startswith(b'%PDF'):
                    # PDF文件
                    doc_type = "pdf"
                    logger.info("根据文件头检测到PDF格式，修正类型为pdf")
                elif content.startswith(b'\xd0\xcf\x11\xe0'):
                    # 旧版Office文档格式 (doc, xls, ppt)
                    doc_type = "office"
                    logger.info("根据文件头检测到旧版Office文档格式，修正类型为office")
                else:
                    # 如果文件头检测失败，回退到文件名检测
                    doc_type = self._get_document_type(doc_name)
                    logger.info(f"文件头检测失败，根据文件名识别类型: {doc_type} (基于文件名: '{doc_name}')")
            else:
                # 没有内容，只能根据文件名判断
                doc_type = self._get_document_type(doc_name)
                logger.info(f"无文件内容，根据文件名识别类型: {doc_type} (基于文件名: '{doc_name}')")

            logger.info(f"最终识别的文档类型: {doc_type}")

            if doc_type == "text":
                # 对于文本文件，直接解析二进制内容
                try:
                    if content and isinstance(content, bytes):
                        # 尝试多种编码解析文本
                        text_content = None
                        for encoding in ['utf-8', 'gbk', 'gb2312', 'latin-1']:
                            try:
                                text_content = content.decode(encoding)
                                logger.info(f"成功使用 {encoding} 编码解析文本内容")
                                break
                            except UnicodeDecodeError:
                                continue

                        if text_content:
                            return {
                                "content_type": "text/plain",
                                "content": text_content,
                                "doc_name": doc_name
                            }
                        else:
                            raise Exception("无法解码文本内容")
                    else:
                        raise Exception("未获取到文档内容")

                except Exception as e:
                    logger.warning(f"解析文本内容失败: {str(e)}")
                    # 如果解析失败，返回下载链接
                    return {
                        "content_type": "text/plain",
                        "url": f"/api/iot/v1/documents/{kb_id}/{doc_id}/download",
                        "doc_name": doc_name,
                        "message": "文本解析失败，请下载查看"
                    }

            elif doc_type == "pdf":
                # 对于PDF文件，直接返回下载URL用于预览
                return {
                    "content_type": "application/pdf",
                    "url": f"/api/iot/v1/documents/{kb_id}/{doc_id}/download",
                    "doc_name": doc_name,
                    "message": "PDF文件可直接预览"
                }

            elif doc_type == "image":
                # 对于图片文件，返回图片URL
                return {
                    "content_type": "image",
                    "url": f"/api/iot/v1/documents/{kb_id}/{doc_id}/download",
                    "doc_name": doc_name,
                    "message": "图片文件可直接预览"
                }

            elif doc_type == "office":
                # Office文档直接返回原始文档URL用于vue-office预览
                logger.info(f"检测到Office文档: {doc_name}，返回原始文档URL用于vue-office预览")

                return {
                    "content_type": "office",
                    "requires_conversion": False,
                    "doc_name": doc_name,
                    "url": f"/api/iot/v1/documents/{kb_id}/{doc_id}/content",  # 使用content端点，不带下载头
                    "download_url": f"/api/iot/v1/documents/{kb_id}/{doc_id}/download",  # 下载仍使用download端点
                    "message": "Office文档支持原生预览",
                    "file_size": len(content) if content else 0
                }

            else:
                # 其他格式提供下载链接
                return {
                    "content_type": "unknown",
                    "url": f"/api/iot/v1/documents/{kb_id}/{doc_id}/download",
                    "doc_name": doc_name,
                    "message": "该文档格式不支持在线预览，请下载查看"
                }

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"获取文档预览失败: {str(e)}")
            raise HTTPException(status_code=500, detail=f"获取文档预览失败: {str(e)}")

    def _is_office_document(self, filename: str) -> bool:
        """检查是否为Office文档"""
        office_extensions = ['.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx']
        return any(filename.lower().endswith(ext) for ext in office_extensions)



    def _get_document_type(self, filename: str) -> str:
        """获取文档类型"""
        if not filename:
            logger.warning("文档名为空，无法识别类型")
            return "unknown"

        filename_lower = filename.lower()

        # 文本文件类型
        text_extensions = ('.txt', '.md', '.json', '.xml', '.csv', '.log', '.yaml', '.yml',
                          '.ini', '.cfg', '.conf', '.py', '.js', '.html', '.css', '.sql',
                          '.sh', '.bat', '.ps1', '.c', '.cpp', '.h', '.java', '.php', '.rb')

        if filename_lower.endswith(text_extensions):
            return "text"
        elif filename_lower.endswith('.pdf'):
            return "pdf"
        elif filename_lower.endswith(('.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.odt', '.ods', '.odp')):
            return "office"
        elif filename_lower.endswith(('.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg', '.tiff', '.ico')):
            return "image"
        else:
            logger.warning(f"未知文档类型: {filename}")
            return "unknown"












# 创建服务实例
document_service = DocumentService()
