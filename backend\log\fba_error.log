2025-08-25 07:57:20.626 | ERROR    | 38c64d53b4144ccd9e44ad637b55063a | JWT 授权异常：cannot access local variable 'json' where it is not associated with a value
Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\jose\jwt.py", line 159, in decode
    payload = jws.verify(token, key, algorithms, verify=verify_signature)
              │   │      │      │    │                  └ True
              │   │      │      │    └ ['HS256']
              │   │      │      └ 'abcdefghijklfastbeesmartrstuvwxyz'
              │   │      └ 'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6Ijg4Yjg4M2M1LWY5MTktNGQ5Zi05NjRkLTc3NTkwYjA1N2E0MyJ9.-uSjWRjxNKNQadRKB9SW6S9hhQ...
              │   └ <function verify at 0x00000262155E3C40>
              └ <module 'jose.jws' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\.venv\\Lib\\site-packages\\jose\\jws.py'>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\jose\jws.py", line 77, in verify
    _verify_signature(signing_input, header, signature, key, algorithms)
    │                 │              │       │          │    └ ['HS256']
    │                 │              │       │          └ 'abcdefghijklfastbeesmartrstuvwxyz'
    │                 │              │       └ b"\xfa\xe4\xa3Y\x18\xf14\xa3Pi\xd4J\x07\xd4\x96\xe9/a\x85\x027K\x03\xc8\xb8\x84\xcf\x9aQ\x01;\xa0\xc8$\x93\xed\x16\xeb\xfd'.\...
    │                 │              └ {'alg': 'HS512'}
    │                 └ b'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6Ijg4Yjg4M2M1LWY5MTktNGQ5Zi05NjRkLTc3NTkwYjA1N2E0MyJ9'
    └ <function _verify_signature at 0x00000262156CF740>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\jose\jws.py", line 259, in _verify_signature
    raise JWSError("The specified alg value is not allowed")
          └ <class 'jose.exceptions.JWSError'>

jose.exceptions.JWSError: The specified alg value is not allowed


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\common\security\jwt.py", line 96, in jwt_decode
    payload = jwt.decode(
              │   └ <function decode at 0x00000262155E3A60>
              └ <module 'jose.jwt' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\.venv\\Lib\\site-packages\\jose\\jwt.py'>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\jose\jwt.py", line 161, in decode
    raise JWTError(e)
          └ <class 'jose.exceptions.JWTError'>

jose.exceptions.JWTError: The specified alg value is not allowed


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\common\security\jwt.py", line 286, in jwt_authentication
    token_payload = jwt_decode(token)
                    │          └ 'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6Ijg4Yjg4M2M1LWY5MTktNGQ5Zi05NjRkLTc3NTkwYjA1N2E0MyJ9.-uSjWRjxNKNQadRKB9SW6S9hhQ...
                    └ <function jwt_decode at 0x0000026215745F80>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\common\security\jwt.py", line 110, in jwt_decode
    raise errors.TokenError(msg='Token 无效')
          │      └ <class 'backend.common.exception.errors.TokenError'>
          └ <module 'backend.common.exception.errors' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\backend\\common\\excepti...

backend.common.exception.errors.TokenError: 401: Token 无效


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\common\security\java_adapter.py", line 89, in authenticate_java_token
    raise errors.TokenError(msg='Token 无效或已过期')
          │      └ <class 'backend.common.exception.errors.TokenError'>
          └ <module 'backend.common.exception.errors' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\backend\\common\\excepti...

backend.common.exception.errors.TokenError: 401: Token 无效或已过期


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\start_stable.py", line 35, in <module>
    main()
    └ <function main at 0x000002620E8AA8E0>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\start_stable.py", line 19, in main
    uvicorn.run(
    │       └ <function run at 0x0000026210DC8B80>
    └ <module 'uvicorn' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\.venv\\Lib\\site-packages\\uvicorn\\__init__.py'>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x0000026210DCBA60>
    └ <uvicorn.server.Server object at 0x0000026218DC0FB0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000026210DCBB00>
           │       │   └ <uvicorn.server.Server object at 0x0000026218DC0FB0>
           │       └ <function run at 0x000002621060F060>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000026218DCA340>
           │      └ <function Runner.run at 0x0000026210AAB2E0>
           └ <asyncio.runners.Runner object at 0x0000026218BA3FB0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-11' coro=<Server.serve() running at C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000026210AA8EA0>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000026218BA3FB0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 674, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000026210B78D60>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 641, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000026210AAAC00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1986, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000026210604860>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x00000262193658A0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026219365D00>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x0000026218E22540>
          └ <backend.middleware.access_middleware.AccessMiddleware object at 0x0000026218EA7410>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
          │    │               │      │        │                     └ Headers({'host': 'localhost:8000', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"Windows"', 'authorization': 'Bearer ey...
          │    │               │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x00000262193658A0>
          │    │               │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026219365D00>
          │    │               └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function CORSMiddleware.simple_response at 0x0000026213DF5260>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x0000026218E22540>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x0000026218E22540...
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026219365D00>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.authentication.AuthenticationMiddleware object at 0x0000026218DE21E0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x0000026218E22540>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\authentication.py", line 36, in __call__
    auth_result = await self.backend.authenticate(conn)
                        │    │       │            └ <starlette.requests.HTTPConnection object at 0x0000026219392BD0>
                        │    │       └ <function JwtAuthMiddleware.authenticate at 0x0000026214DFB7E0>
                        │    └ <backend.middleware.jwt_auth_middleware.JwtAuthMiddleware object at 0x0000026216AFE5A0>
                        └ <starlette.middleware.authentication.AuthenticationMiddleware object at 0x0000026218DE21E0>

> File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\middleware\jwt_auth_middleware.py", line 74, in authenticate
    user = await jwt_authentication(token)
                 │                  └ 'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6Ijg4Yjg4M2M1LWY5MTktNGQ5Zi05NjRkLTc3NTkwYjA1N2E0MyJ9.-uSjWRjxNKNQadRKB9SW6S9hhQ...
                 └ <function jwt_authentication at 0x0000026215746480>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\common\security\jwt.py", line 315, in jwt_authentication
    return await java_adapter.authenticate_java_token(token)
                 │            │                       └ 'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6Ijg4Yjg4M2M1LWY5MTktNGQ5Zi05NjRkLTc3NTkwYjA1N2E0MyJ9.-uSjWRjxNKNQadRKB9SW6S9hhQ...
                 │            └ <function JavaAdapter.authenticate_java_token at 0x0000026215745C60>
                 └ <backend.common.security.java_adapter.JavaAdapter object at 0x0000026216725B50>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\common\security\java_adapter.py", line 108, in authenticate_java_token
    except json.JSONDecodeError as e:
           │    └ <class 'json.decoder.JSONDecodeError'>
           └ <module 'json' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\json\\__init__.py'>

UnboundLocalError: cannot access local variable 'json' where it is not associated with a value
2025-08-25 07:57:22.107 | ERROR    | d28dcea8805a4b538463f2113e6502f5 | JWT 授权异常：cannot access local variable 'json' where it is not associated with a value
Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\jose\jwt.py", line 159, in decode
    payload = jws.verify(token, key, algorithms, verify=verify_signature)
              │   │      │      │    │                  └ True
              │   │      │      │    └ ['HS256']
              │   │      │      └ 'abcdefghijklfastbeesmartrstuvwxyz'
              │   │      └ 'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6Ijg4Yjg4M2M1LWY5MTktNGQ5Zi05NjRkLTc3NTkwYjA1N2E0MyJ9.-uSjWRjxNKNQadRKB9SW6S9hhQ...
              │   └ <function verify at 0x00000262155E3C40>
              └ <module 'jose.jws' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\.venv\\Lib\\site-packages\\jose\\jws.py'>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\jose\jws.py", line 77, in verify
    _verify_signature(signing_input, header, signature, key, algorithms)
    │                 │              │       │          │    └ ['HS256']
    │                 │              │       │          └ 'abcdefghijklfastbeesmartrstuvwxyz'
    │                 │              │       └ b"\xfa\xe4\xa3Y\x18\xf14\xa3Pi\xd4J\x07\xd4\x96\xe9/a\x85\x027K\x03\xc8\xb8\x84\xcf\x9aQ\x01;\xa0\xc8$\x93\xed\x16\xeb\xfd'.\...
    │                 │              └ {'alg': 'HS512'}
    │                 └ b'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6Ijg4Yjg4M2M1LWY5MTktNGQ5Zi05NjRkLTc3NTkwYjA1N2E0MyJ9'
    └ <function _verify_signature at 0x00000262156CF740>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\jose\jws.py", line 259, in _verify_signature
    raise JWSError("The specified alg value is not allowed")
          └ <class 'jose.exceptions.JWSError'>

jose.exceptions.JWSError: The specified alg value is not allowed


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\common\security\jwt.py", line 96, in jwt_decode
    payload = jwt.decode(
              │   └ <function decode at 0x00000262155E3A60>
              └ <module 'jose.jwt' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\.venv\\Lib\\site-packages\\jose\\jwt.py'>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\jose\jwt.py", line 161, in decode
    raise JWTError(e)
          └ <class 'jose.exceptions.JWTError'>

jose.exceptions.JWTError: The specified alg value is not allowed


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\common\security\jwt.py", line 286, in jwt_authentication
    token_payload = jwt_decode(token)
                    │          └ 'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6Ijg4Yjg4M2M1LWY5MTktNGQ5Zi05NjRkLTc3NTkwYjA1N2E0MyJ9.-uSjWRjxNKNQadRKB9SW6S9hhQ...
                    └ <function jwt_decode at 0x0000026215745F80>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\common\security\jwt.py", line 110, in jwt_decode
    raise errors.TokenError(msg='Token 无效')
          │      └ <class 'backend.common.exception.errors.TokenError'>
          └ <module 'backend.common.exception.errors' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\backend\\common\\excepti...

backend.common.exception.errors.TokenError: 401: Token 无效


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\common\security\java_adapter.py", line 89, in authenticate_java_token
    raise errors.TokenError(msg='Token 无效或已过期')
          │      └ <class 'backend.common.exception.errors.TokenError'>
          └ <module 'backend.common.exception.errors' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\backend\\common\\excepti...

backend.common.exception.errors.TokenError: 401: Token 无效或已过期


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\start_stable.py", line 35, in <module>
    main()
    └ <function main at 0x000002620E8AA8E0>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\start_stable.py", line 19, in main
    uvicorn.run(
    │       └ <function run at 0x0000026210DC8B80>
    └ <module 'uvicorn' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\.venv\\Lib\\site-packages\\uvicorn\\__init__.py'>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x0000026210DCBA60>
    └ <uvicorn.server.Server object at 0x0000026218DC0FB0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000026210DCBB00>
           │       │   └ <uvicorn.server.Server object at 0x0000026218DC0FB0>
           │       └ <function run at 0x000002621060F060>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000026218DCA340>
           │      └ <function Runner.run at 0x0000026210AAB2E0>
           └ <asyncio.runners.Runner object at 0x0000026218BA3FB0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-11' coro=<Server.serve() running at C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000026210AA8EA0>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000026218BA3FB0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 674, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000026210B78D60>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 641, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000026210AAAC00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1986, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000026210604860>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026219367920>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000262193679C0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x0000026218E22540>
          └ <backend.middleware.access_middleware.AccessMiddleware object at 0x0000026218EA7410>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
          │    │               │      │        │                     └ Headers({'host': 'localhost:8000', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"Windows"', 'authorization': 'Bearer ey...
          │    │               │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026219367920>
          │    │               │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000262193679C0>
          │    │               └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function CORSMiddleware.simple_response at 0x0000026213DF5260>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x0000026218E22540>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x0000026218E22540...
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000262193679C0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.authentication.AuthenticationMiddleware object at 0x0000026218DE21E0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x0000026218E22540>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\authentication.py", line 36, in __call__
    auth_result = await self.backend.authenticate(conn)
                        │    │       │            └ <starlette.requests.HTTPConnection object at 0x00000262193019D0>
                        │    │       └ <function JwtAuthMiddleware.authenticate at 0x0000026214DFB7E0>
                        │    └ <backend.middleware.jwt_auth_middleware.JwtAuthMiddleware object at 0x0000026216AFE5A0>
                        └ <starlette.middleware.authentication.AuthenticationMiddleware object at 0x0000026218DE21E0>

> File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\middleware\jwt_auth_middleware.py", line 74, in authenticate
    user = await jwt_authentication(token)
                 │                  └ 'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6Ijg4Yjg4M2M1LWY5MTktNGQ5Zi05NjRkLTc3NTkwYjA1N2E0MyJ9.-uSjWRjxNKNQadRKB9SW6S9hhQ...
                 └ <function jwt_authentication at 0x0000026215746480>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\common\security\jwt.py", line 315, in jwt_authentication
    return await java_adapter.authenticate_java_token(token)
                 │            │                       └ 'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6Ijg4Yjg4M2M1LWY5MTktNGQ5Zi05NjRkLTc3NTkwYjA1N2E0MyJ9.-uSjWRjxNKNQadRKB9SW6S9hhQ...
                 │            └ <function JavaAdapter.authenticate_java_token at 0x0000026215745C60>
                 └ <backend.common.security.java_adapter.JavaAdapter object at 0x0000026216725B50>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\common\security\java_adapter.py", line 108, in authenticate_java_token
    except json.JSONDecodeError as e:
           │    └ <class 'json.decoder.JSONDecodeError'>
           └ <module 'json' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\json\\__init__.py'>

UnboundLocalError: cannot access local variable 'json' where it is not associated with a value
2025-08-25 08:06:11.655 | ERROR    | 8c857efa480148068bb98e4de341d2d6 | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 08:06:11.655 | ERROR    | 8c857efa480148068bb98e4de341d2d6 | 开始文档解析失败: Can't stop parsing document with progress at 0 or 100
2025-08-25 08:06:16.254 | ERROR    | 11aa3b126373401d9e55903aa0332da6 | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 08:06:16.254 | ERROR    | 11aa3b126373401d9e55903aa0332da6 | 开始文档解析失败: Can't stop parsing document with progress at 0 or 100
2025-08-25 08:06:17.921 | ERROR    | 069e7893bbf14eb5a3e100305d8aff2e | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 08:06:17.922 | ERROR    | 069e7893bbf14eb5a3e100305d8aff2e | 开始文档解析失败: Can't stop parsing document with progress at 0 or 100
2025-08-25 08:06:20.809 | ERROR    | 4472bb9b37594457af277f2dd89cb662 | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 08:06:20.809 | ERROR    | 4472bb9b37594457af277f2dd89cb662 | 开始文档解析失败: Can't stop parsing document with progress at 0 or 100
2025-08-25 08:06:27.015 | ERROR    | 5ef6abd64e0f417faef4be72464365f5 | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 08:06:27.016 | ERROR    | 5ef6abd64e0f417faef4be72464365f5 | 开始文档解析失败: Can't stop parsing document with progress at 0 or 100
2025-08-25 08:13:23.186 | ERROR    | f38b54c8628e4f9f8574c6023a6d92ec | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 08:13:23.186 | ERROR    | f38b54c8628e4f9f8574c6023a6d92ec | 开始文档解析失败: Can't stop parsing document with progress at 0 or 100
2025-08-25 08:13:27.671 | ERROR    | a9d72d745f014795af08157a45bdf5e5 | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 08:13:27.671 | ERROR    | a9d72d745f014795af08157a45bdf5e5 | 开始文档解析失败: Can't stop parsing document with progress at 0 or 100
2025-08-25 08:19:19.886 | ERROR    | bf8b0e19cfe6424086b5b25a7f62ea38 | RAGFlow业务错误: code=100, message=<NotFound '404: Not Found'>
2025-08-25 08:19:19.887 | ERROR    | bf8b0e19cfe6424086b5b25a7f62ea38 | 开始文档解析失败: <NotFound '404: Not Found'>
2025-08-25 08:19:30.663 | ERROR    | 24c1809510374767b7e66151fde14b9b | RAGFlow业务错误: code=100, message=<NotFound '404: Not Found'>
2025-08-25 08:19:30.663 | ERROR    | 24c1809510374767b7e66151fde14b9b | 开始文档解析失败: <NotFound '404: Not Found'>
2025-08-25 08:20:57.982 | ERROR    | bfa88eb42618494eb0ac87a5e030e847 | RAGFlow业务错误: code=401, message=<Unauthorized '401: Unauthorized'>
2025-08-25 08:20:57.982 | ERROR    | bfa88eb42618494eb0ac87a5e030e847 | 开始文档解析失败: <Unauthorized '401: Unauthorized'>
